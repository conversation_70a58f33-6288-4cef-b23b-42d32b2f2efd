/**
 * Enhanced Pipeline Visualization Component
 * Supports both compact popup view and expanded layouts (right-side panel or full-screen modal)
 * Features live console logs spanning vertically across whole available space
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, Maximize2, Minimize2, PanelRight, Fullscreen, Play, Pause, RotateCcw } from 'lucide-react';
import PipelineStepCard from './PipelineStepCard.jsx';
import LiveConsoleLogger from './LiveConsoleLogger.jsx';
import { PIPELINE_STEPS, STEP_STATUS, getNextAvailableSteps } from '../../../core/config/pipelineSteps.js';
import { documentProcessingPipeline } from '../../../services/DocumentProcessingPipeline.js';

// Layout modes
const LAYOUT_MODES = {
  COMPACT: 'compact', // Original squeezed layout in popup
  RIGHT_PANEL: 'right_panel', // Right-side panel layout
  FULL_SCREEN: 'full_screen' // Full-screen modal layout
};

// View modes for pipeline steps
const VIEW_MODES = {
  COMPACT: 'compact', // Minimal information, expandable blocks
  DETAILED: 'detailed' // Full information display
};

const EnhancedPipelineVisualization = ({
  file = null,
  isProcessing = false,
  onProcessingChange,
  onStepComplete,
  onError,
  autoRun = false,
  initialLayout = LAYOUT_MODES.COMPACT,
  initialViewMode = VIEW_MODES.COMPACT, // New prop for view mode
  windowMode = false // New prop to indicate if running in popup window
}) => {
  const [layoutMode, setLayoutMode] = useState(initialLayout);
  const [viewMode, setViewMode] = useState(initialViewMode);
  const [pipelineState, setPipelineState] = useState({
    steps: {},
    currentStep: null,
    completedSteps: [],
    errors: {},
    results: {},
    timings: {},
    overallProgress: 0
  });

  const [consoleLogs, setConsoleLogs] = useState([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const modalRef = useRef(null);

  // Add log entry to console with live updates
  const addLog = useCallback((level, message, stepName = null, data = null) => {
    const logEntry = {
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
      level,
      message,
      stepName,
      data
    };
    setConsoleLogs(prev => [...prev, logEntry]);
  }, []);

  // Clear console logs
  const clearLogs = useCallback(() => {
    setConsoleLogs([]);
  }, []);

  // Export console logs
  const exportLogs = useCallback(() => {
    const logData = consoleLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      step: log.stepName || 'system',
      message: log.message,
      data: log.data
    }));

    const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pipeline-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [consoleLogs]);

  // Handle layout mode changes
  const handleLayoutChange = useCallback((newMode) => {
    setLayoutMode(newMode);
    if (newMode === LAYOUT_MODES.FULL_SCREEN) {
      setIsExpanded(true);
    } else if (newMode === LAYOUT_MODES.COMPACT) {
      setIsExpanded(false);
    }
  }, []);

  // Handle escape key for full-screen mode
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && layoutMode === LAYOUT_MODES.FULL_SCREEN) {
        handleLayoutChange(LAYOUT_MODES.COMPACT);
      }
    };

    if (layoutMode === LAYOUT_MODES.FULL_SCREEN) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [layoutMode, handleLayoutChange]);

  // Update step state
  const updateStepState = useCallback((stepId, updates) => {
    setPipelineState(prev => ({
      ...prev,
      steps: {
        ...prev.steps,
        [stepId]: {
          ...prev.steps[stepId],
          ...updates
        }
      }
    }));
  }, []);

  // Calculate overall progress
  const calculateOverallProgress = useCallback((completedSteps, currentStep, currentProgress) => {
    const totalSteps = PIPELINE_STEPS.length;
    const completedCount = completedSteps.length;
    const currentStepProgress = currentProgress || 0;

    return Math.round(((completedCount + (currentStepProgress / 100)) / totalSteps) * 100);
  }, []);

  // Run full pipeline
  const runFullPipeline = useCallback(async () => {
    if (!file || isProcessing) { return; }

    try {
      addLog('info', '🚀 Starting full pipeline execution', null, { fileName: file.name });
      onProcessingChange?.(true);

      // Get API key from environment variables
      const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        addLog('warning', '⚠️ No DeepSeek API key found in environment variables');
      } else {
        addLog('info', '✅ DeepSeek API key loaded from environment');
      }

      const progressCallback = (stepId, progress, message) => {
        updateStepState(stepId, {
          status: STEP_STATUS.RUNNING,
          progress: progress
        });

        setPipelineState(prev => ({
          ...prev,
          currentStep: stepId,
          overallProgress: calculateOverallProgress(prev.completedSteps, stepId, progress)
        }));

        if (message) {
          addLog('info', message, stepId, { progress });
        }
      };

      // Execute pipeline steps sequentially
      for (const step of PIPELINE_STEPS) {
        try {
          addLog('info', `🔄 Starting step: ${step.name}`, step.id);
          updateStepState(step.id, { status: STEP_STATUS.RUNNING, progress: 0 });

          const startTime = Date.now();
          const result = await runSingleStep(step.id, file, { progressCallback, apiKey });
          const endTime = Date.now();
          const timing = endTime - startTime;

          updateStepState(step.id, {
            status: STEP_STATUS.COMPLETED,
            progress: 100,
            result: result,
            timing: timing
          });

          setPipelineState(prev => ({
            ...prev,
            completedSteps: [...prev.completedSteps, step.id],
            results: { ...prev.results, [step.id]: result },
            timings: { ...prev.timings, [step.id]: timing }
          }));

          addLog('success', `✅ Completed step: ${step.name} (${timing}ms)`, step.id, result);
          onStepComplete?.(result);

        } catch (error) {
          const errorMessage = error.message || 'Unknown error';
          addLog('error', `❌ Failed step: ${step.name} - ${errorMessage}`, step.id, { error: errorMessage });

          updateStepState(step.id, {
            status: STEP_STATUS.ERROR,
            error: errorMessage
          });

          setPipelineState(prev => ({
            ...prev,
            errors: { ...prev.errors, [step.id]: errorMessage }
          }));

          onError?.(errorMessage);
          break; // Stop pipeline on error
        }
      }

      addLog('info', '🎉 Pipeline execution completed');

    } catch (error) {
      const errorMessage = error.message || 'Pipeline execution failed';
      addLog('error', `❌ Pipeline failed: ${errorMessage}`, null, { error: errorMessage });
      onError?.(errorMessage);
    } finally {
      onProcessingChange?.(false);
      setPipelineState(prev => ({ ...prev, currentStep: null }));
    }
  }, [file, isProcessing, onProcessingChange, onStepComplete, onError, addLog, updateStepState, calculateOverallProgress]);

  // Run single pipeline step with live logging
  const runSingleStep = useCallback(async (stepId, file, options = {}) => {
    const { progressCallback, apiKey } = options;

    // Add live log for step start
    addLog('info', `🔄 Executing step: ${stepId}`, stepId);

    let result;
    try {
      switch (stepId) {
        case 'pdf_extraction':
          addLog('debug', '📄 Starting PDF text extraction...', stepId);
          result = await documentProcessingPipeline.runPdfExtraction(file, {
            progressCallback: (progress) => {
              addLog('debug', `📄 PDF extraction progress: ${progress}%`, stepId);
              progressCallback?.(stepId, progress, `Extracting PDF content: ${progress}%`);
            }
          });
          break;

        case 'deepseek_analysis_1':
        case 'deepseek_analysis_2':
        case 'deepseek_analysis_3':
          if (!apiKey) {
            throw new Error('DeepSeek API key required for analysis');
          }
          addLog('debug', '🤖 Starting DeepSeek AI analysis...', stepId);

          // Get PDF text from previous step
          const pdfResult = pipelineState.results.pdf_extraction;
          if (!pdfResult?.data?.text) {
            throw new Error('PDF extraction result required for DeepSeek analysis');
          }

          result = await documentProcessingPipeline.runDeepSeekAnalysis(pdfResult.data.text, {
            progressCallback: (progress) => {
              addLog('debug', `🤖 DeepSeek analysis progress: ${progress}%`, stepId);
              progressCallback?.(stepId, progress, `AI analysis: ${progress}%`);
            },
            apiKey,
            language: 'pol',
            companyInfo: {
              name: window.__MVAT_ENV__?.COMPANY_NAME || 'MVAT Solutions'
            }
          });
          break;

        case 'rag_enhancement':
          addLog('debug', '🔗 Starting RAG document linking...', stepId);
          result = await documentProcessingPipeline.runRagDocumentLinking?.(file, {
            progressCallback: (progress) => {
              addLog('debug', `🔗 RAG linking progress: ${progress}%`, stepId);
              progressCallback?.(stepId, progress, `Document linking: ${progress}%`);
            }
          }) || { success: true, data: { message: 'RAG linking simulated' } };
          break;

        case 'tesseract_reference':
          addLog('debug', '👁️ Starting OCR structural reference...', stepId);
          result = await documentProcessingPipeline.runOcrStructuralReference?.(file, {
            progressCallback: (progress) => {
              addLog('debug', `👁️ OCR progress: ${progress}%`, stepId);
              progressCallback?.(stepId, progress, `OCR processing: ${progress}%`);
            }
          }) || { success: true, data: { message: 'OCR reference simulated' } };
          break;

        case 'final_output':
          addLog('debug', '🎯 Generating final output...', stepId);
          result = await documentProcessingPipeline.runFinalOutputGeneration?.(file, {
            progressCallback: (progress) => {
              addLog('debug', `🎯 Final output progress: ${progress}%`, stepId);
              progressCallback?.(stepId, progress, `Generating output: ${progress}%`);
            }
          }) || { success: true, data: { message: 'Final output generated' } };
          break;

        default:
          throw new Error(`Unknown step: ${stepId}`);
      }

      // Add success log
      addLog('success', `✅ Step completed successfully: ${stepId}`, stepId, result?.data);
      return result;

    } catch (error) {
      // Add error log
      addLog('error', `❌ Step failed: ${error.message}`, stepId, { error: error.message });
      throw error;
    }
  }, [addLog, pipelineState.results]);

  // Handle step actions
  const handleStepAction = useCallback(async (stepId, actionType) => {
    if (actionType === 'rerun') {
      try {
        addLog('info', `🔄 Rerunning step: ${stepId}`);
        const apiKey = window.__MVAT_ENV__?.DEEPSEEK_API_KEY || process.env.DEEPSEEK_API_KEY;
        const result = await runSingleStep(stepId, file, { apiKey });
        addLog('success', `✅ Step rerun completed: ${stepId}`, stepId, result);
      } catch (error) {
        addLog('error', `❌ Step rerun failed: ${stepId} - ${error.message}`, stepId);
      }
    }
  }, [file, addLog, runSingleStep]);

  // Auto-run pipeline when file is provided
  useEffect(() => {
    if (file && autoRun && !isProcessing) {
      runFullPipeline();
    }
  }, [file, autoRun, isProcessing, runFullPipeline]);

  // Render enhanced pipeline arrows
  const renderPipelineArrow = (index) => {
    if (index >= PIPELINE_STEPS.length - 1) { return null; }

    const currentStep = PIPELINE_STEPS[index];
    const nextStep = PIPELINE_STEPS[index + 1];
    const currentStatus = pipelineState.steps[currentStep.id]?.status;
    const nextStatus = pipelineState.steps[nextStep.id]?.status;

    const isCurrentCompleted = currentStatus === STEP_STATUS.COMPLETED;
    const isNextActive = nextStatus === STEP_STATUS.RUNNING;

    return (
      <div className="flex justify-center py-3">
        <div className="flex flex-col items-center space-y-1">
          {/* Animated arrow */}
          <div className={`relative flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
            isCurrentCompleted ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
          }`}>
            <div className={`text-lg transition-transform duration-300 ${
              isNextActive ? 'animate-bounce' : ''
            }`}>
              ⬇️
            </div>
            {isNextActive && (
              <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 animate-ping" />
            )}
          </div>

          {/* Connection line */}
          <div className={`w-0.5 h-4 transition-colors duration-300 ${
            isCurrentCompleted ? 'bg-blue-500' : 'bg-gray-300'
          }`} />
        </div>
      </div>
    );
  };

  // Render layout controls
  const renderLayoutControls = () => (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.COMPACT ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Compact View"
      >
        <Minimize2 size={16} />
      </button>
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.RIGHT_PANEL)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.RIGHT_PANEL ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Right Panel View"
      >
        <PanelRight size={16} />
      </button>
      <button
        onClick={() => handleLayoutChange(LAYOUT_MODES.FULL_SCREEN)}
        className={`p-2 rounded ${layoutMode === LAYOUT_MODES.FULL_SCREEN ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
        title="Full Screen View"
      >
        <Fullscreen size={16} />
      </button>
    </div>
  );

  // Render view mode controls - extension styled
  const renderViewModeControls = () => (
    <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
      <button
        onClick={() => setViewMode(VIEW_MODES.COMPACT)}
        className={`px-3 py-1.5 text-xs rounded-md font-medium transition-all duration-200 ${
          viewMode === VIEW_MODES.COMPACT
            ? 'bg-white text-primary-700 shadow-sm border border-primary-200'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
        }`}
        title="Compact View - Clean pipeline steps only, no console logs"
      >
        Clean
      </button>
      <button
        onClick={() => setViewMode(VIEW_MODES.DETAILED)}
        className={`px-3 py-1.5 text-xs rounded-md font-medium transition-all duration-200 ${
          viewMode === VIEW_MODES.DETAILED
            ? 'bg-white text-primary-700 shadow-sm border border-primary-200'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
        }`}
        title="Detailed View - Full information with console logs"
      >
        Full
      </button>
    </div>
  );

  // Render compact layout (takes all available container space) - extension styled
  const renderCompactLayout = () => (
    <div style={windowMode ? {
      width: '100%',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: 'white'
    } : {}} className={!windowMode ? 'w-full h-full flex flex-col bg-white' : ''}>
      {/* Header with layout and view controls - extension styled */}
      <div style={windowMode ? {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1rem',
        padding: '1rem',
        backgroundColor: 'white',
        borderBottom: '1px solid #e5e7eb'
      } : {}} className={!windowMode ? 'flex items-center justify-between mb-4 p-4 bg-white border-b border-gray-200' : ''}>
        <div style={windowMode ? {
          display: 'flex',
          alignItems: 'center',
          gap: '1rem'
        } : {}} className={!windowMode ? 'flex items-center space-x-4' : ''}>
          <h3 style={windowMode ? {
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#111827',
            margin: 0
          } : {}} className={!windowMode ? 'text-lg font-semibold text-gray-900' : ''}>Multi-Step Pipeline</h3>
          {renderViewModeControls()}
        </div>
        <div style={windowMode ? {
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        } : {}} className={!windowMode ? 'flex items-center space-x-2' : ''}>
          {!windowMode && renderLayoutControls()}
          {file && (
            <button
              onClick={runFullPipeline}
              disabled={isProcessing}
              style={windowMode ? {
                backgroundColor: isProcessing ? '#9ca3af' : '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '0.375rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                cursor: isProcessing ? 'not-allowed' : 'pointer',
                opacity: isProcessing ? 0.5 : 1
              } : {}}
              className={!windowMode ? 'btn btn-primary text-sm flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed' : ''}
            >
              {isProcessing ? (
                <>
                  <div style={windowMode ? {
                    width: '12px',
                    height: '12px',
                    border: '2px solid transparent',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  } : {}} className={!windowMode ? 'animate-spin rounded-full h-3 w-3 border-b-2 border-white' : ''} />
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <span>🚀</span>
                  <span>Run Pipeline</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Progress bar */}
      {isProcessing && (
        <div style={windowMode ? {
          marginBottom: '0.75rem',
          padding: '0 1rem'
        } : {}} className={!windowMode ? 'mb-3' : ''}>
          <div style={windowMode ? {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            fontSize: '0.875rem',
            color: '#4b5563',
            marginBottom: '0.25rem'
          } : {}} className={!windowMode ? 'flex items-center justify-between text-sm text-gray-600 mb-1' : ''}>
            <span>Overall Progress</span>
            <span>{pipelineState.overallProgress}%</span>
          </div>
          <div style={windowMode ? {
            width: '100%',
            backgroundColor: '#e5e7eb',
            borderRadius: '9999px',
            height: '8px'
          } : {}} className={!windowMode ? 'w-full bg-gray-200 rounded-full h-2' : ''}>
            <div
              style={windowMode ? {
                backgroundColor: '#3b82f6',
                height: '8px',
                borderRadius: '9999px',
                transition: 'all 0.3s ease',
                width: `${pipelineState.overallProgress}%`
              } : { width: `${pipelineState.overallProgress}%` }}
              className={!windowMode ? 'bg-blue-500 h-2 rounded-full transition-all duration-300' : ''}
            />
          </div>
        </div>
      )}

      {/* Pipeline steps - Full width in compact mode, split in detailed mode */}
      <div style={windowMode ? {
        flex: 1,
        display: 'flex',
        gap: '1rem',
        minHeight: 0,
        padding: '0 1rem'
      } : {}} className={!windowMode ? 'flex-1 flex gap-4 min-h-0' : ''}>
        {/* Pipeline steps */}
        <div style={windowMode ? {
          width: viewMode === VIEW_MODES.COMPACT ? '100%' : '50%',
          minWidth: 0
        } : {}} className={!windowMode ? `${viewMode === VIEW_MODES.COMPACT ? 'w-full' : 'flex-1'} min-w-0` : ''}>
          <div style={windowMode ? {
            height: '100%',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          } : {}} className={!windowMode ? 'h-full overflow-y-auto space-y-2' : ''}>
            {PIPELINE_STEPS.map((step, index) => (
              <div key={step.id}>
                <PipelineStepCard
                  step={step}
                  status={pipelineState.steps[step.id]?.status}
                  progress={pipelineState.steps[step.id]?.progress}
                  result={pipelineState.steps[step.id]?.result}
                  error={pipelineState.steps[step.id]?.error}
                  timing={pipelineState.steps[step.id]?.timing}
                  onAction={handleStepAction}
                  windowMode={windowMode}
                  isActive={pipelineState.currentStep === step.id}
                  showExpandableData={viewMode === VIEW_MODES.DETAILED}
                  compactMode={viewMode === VIEW_MODES.COMPACT}
                />
                {renderPipelineArrow(index)}
              </div>
            ))}
          </div>
        </div>

        {/* Console logs - Only shown in detailed mode */}
        {viewMode === VIEW_MODES.DETAILED && (
          <div className="w-80 min-w-80 flex flex-col">
            <div className="mb-2 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900">Console Logs</h4>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Live</span>
            </div>
            <div className="flex-1 min-h-0">
              <LiveConsoleLogger
                logs={consoleLogs}
                isProcessing={isProcessing}
                onClear={clearLogs}
                maxLogs={500}
                autoScroll={true}
                className="h-full"
                compact={false}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Render right panel layout (polished)
  const renderRightPanelLayout = () => (
    <div className="fixed inset-y-0 right-0 w-4/5 bg-white shadow-2xl z-50 flex flex-col border-l border-gray-300">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Multi-Step Processing Pipeline</h3>
          <p className="text-sm text-gray-600 mt-1">
            {file ? `Processing: ${file.name}` : 'No file selected'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {renderLayoutControls()}
          <button
            onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-white/50"
            title="Close Panel"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      {/* Progress bar */}
      {isProcessing && (
        <div className="px-4 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
            <span>Overall Progress</span>
            <span>{pipelineState.overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${pipelineState.overallProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Main content - Improved split layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left: Pipeline Steps */}
        <div className="w-2/5 border-r border-gray-200 overflow-y-auto bg-gray-50/30">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-lg font-medium text-gray-900">Pipeline Steps</h4>
              {file && (
                <button
                  onClick={runFullPipeline}
                  disabled={isProcessing}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 disabled:bg-gray-400 flex items-center space-x-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white" />
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <span>🚀</span>
                      <span>Run Pipeline</span>
                    </>
                  )}
                </button>
              )}
            </div>
            <div className="space-y-4">
              {PIPELINE_STEPS.map((step, index) => (
                <div key={step.id}>
                  <PipelineStepCard
                    step={step}
                    status={pipelineState.steps[step.id]?.status}
                    progress={pipelineState.steps[step.id]?.progress}
                    result={pipelineState.steps[step.id]?.result}
                    error={pipelineState.steps[step.id]?.error}
                    timing={pipelineState.steps[step.id]?.timing}
                    onAction={handleStepAction}
                    isActive={pipelineState.currentStep === step.id}
                    showExpandableData={true}
                  />
                  {renderPipelineArrow(index)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right: Live Console Logs - Enhanced */}
        <div className="w-3/5 flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-gray-900 text-white">
            <h4 className="text-lg font-medium">Live Console Logs</h4>
            <p className="text-sm text-gray-300">Real-time processing logs with full panel height</p>
          </div>
          <div className="flex-1 overflow-hidden bg-gray-900">
            <LiveConsoleLogger
              logs={consoleLogs}
              isProcessing={isProcessing}
              onClear={clearLogs}
              onExport={exportLogs}
              maxLogs={1500}
              autoScroll={true}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </div>
  );

  // Render full screen layout (truly fullscreen)
  const renderFullScreenLayout = () => (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div>
          <h3 className="text-2xl font-semibold text-gray-900">Multi-Step Processing Pipeline</h3>
          <p className="text-sm text-gray-600 mt-1">
            {file ? `Processing: ${file.name}` : 'No file selected'}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {renderLayoutControls()}
          <button
            onClick={() => handleLayoutChange(LAYOUT_MODES.COMPACT)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
            title="Close Full Screen"
          >
            <X size={24} />
          </button>
        </div>
      </div>

      {/* Progress bar */}
      {isProcessing && (
        <div className="px-6 py-3 border-b border-gray-200 bg-blue-50">
          <div className="flex items-center justify-between text-sm text-gray-700 mb-2">
            <span className="font-medium">Overall Progress</span>
            <span className="font-mono">{pipelineState.overallProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-blue-500 h-3 rounded-full transition-all duration-300 relative overflow-hidden"
              style={{ width: `${pipelineState.overallProgress}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
            </div>
          </div>
        </div>
      )}

      {/* Main content - Split layout with maximum space */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left: Pipeline Steps - Wider in full screen */}
        <div className="w-2/5 border-r border-gray-200 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-xl font-medium text-gray-900">Pipeline Steps</h4>
              {file && (
                <button
                  onClick={runFullPipeline}
                  disabled={isProcessing}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center space-x-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                      <span>Processing...</span>
                    </>
                  ) : (
                    <>
                      <Play size={16} />
                      <span>Run Full Pipeline</span>
                    </>
                  )}
                </button>
              )}
            </div>
            <div className="space-y-4">
              {PIPELINE_STEPS.map((step, index) => (
                <div key={step.id}>
                  <PipelineStepCard
                    step={step}
                    status={pipelineState.steps[step.id]?.status}
                    progress={pipelineState.steps[step.id]?.progress}
                    result={pipelineState.steps[step.id]?.result}
                    error={pipelineState.steps[step.id]?.error}
                    timing={pipelineState.steps[step.id]?.timing}
                    onAction={handleStepAction}
                    isActive={pipelineState.currentStep === step.id}
                    showExpandableData={true}
                  />
                  {renderPipelineArrow(index)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right: Live Console Logs - Maximum vertical space */}
        <div className="w-3/5 flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <h4 className="text-xl font-medium text-gray-900">Live Console Logs</h4>
            <p className="text-sm text-gray-600">Real-time processing logs spanning full screen height</p>
          </div>
          <div className="flex-1 overflow-hidden">
            <LiveConsoleLogger
              logs={consoleLogs}
              isProcessing={isProcessing}
              onClear={clearLogs}
              onExport={exportLogs}
              maxLogs={2000}
              autoScroll={true}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </div>
  );

  // Main render logic
  if (layoutMode === LAYOUT_MODES.COMPACT) {
    return renderCompactLayout();
  } else if (layoutMode === LAYOUT_MODES.RIGHT_PANEL) {
    return renderRightPanelLayout();
  } else if (layoutMode === LAYOUT_MODES.FULL_SCREEN) {
    return renderFullScreenLayout();
  }

  return renderCompactLayout();
};

export default EnhancedPipelineVisualization;
export { LAYOUT_MODES, VIEW_MODES };
