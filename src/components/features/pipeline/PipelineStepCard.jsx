/**
 * PipelineStepCard - Individual step component for the multi-step pipeline
 * Displays step status, progress, and action buttons
 */

import React, { useState } from 'react';
import { getStepColors, ACTION_TYPES, STEP_STATUS } from '../../../core/config/pipelineSteps.js';

const PipelineStepCard = ({
  step,
  status = STEP_STATUS.PENDING,
  progress = 0,
  result = null,
  error = null,
  timing = 0,
  onAction,
  isActive = false,
  showDetails = false,
  showExpandableData = false,
  compactMode = false, // New prop for compact display
  windowMode = false // New prop for window mode with inline styles
}) => {
  const [expanded, setExpanded] = useState(showDetails);
  const [showRawInput, setShowRawInput] = useState(false);
  const [showRawOutput, setShowRawOutput] = useState(false);
  const colors = getStepColors(step.color);

  const getStatusIcon = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return '✅';
      case STEP_STATUS.RUNNING:
        return '⏳';
      case STEP_STATUS.ERROR:
        return '❌';
      case STEP_STATUS.SKIPPED:
        return '⏭️';
      default:
        return '⏸️';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return 'text-green-600';
      case STEP_STATUS.RUNNING:
        return 'text-blue-600';
      case STEP_STATUS.ERROR:
        return 'text-red-600';
      case STEP_STATUS.SKIPPED:
        return 'text-gray-500';
      default:
        return 'text-gray-400';
    }
  };

  const handleAction = (actionType) => {
    // Handle fold/unfold actions locally
    if (actionType === ACTION_TYPES.VIEW_RAW) {
      setShowRawInput(!showRawInput);
      return;
    }
    if (actionType === ACTION_TYPES.VIEW_OUTPUT) {
      setShowRawOutput(!showRawOutput);
      return;
    }

    // Pass other actions to parent
    if (onAction) {
      onAction(step.id, actionType);
    }
  };

  const formatTiming = (ms) => {
    if (ms < 1000) { return `${ms}ms`; }
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getActionButtons = () => {
    const buttons = [];

    step.actions.forEach(action => {
      let label, variant, disabled;

      switch (action) {
        case ACTION_TYPES.RERUN:
          label = '🔄 Rerun';
          variant = 'secondary';
          disabled = status === STEP_STATUS.RUNNING;
          break;
        case ACTION_TYPES.VIEW_RAW:
          label = showRawInput ? '📄 Hide Input' : '📄 Show Input';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_OUTPUT:
          label = showRawOutput ? '📊 Hide Output' : '📊 Show Output';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.ENHANCE_PROMPT:
          label = '✨ Enhance';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.VIEW_SIMILAR:
          label = '🔗 Similar';
          variant = 'outline';
          disabled = !result?.similar_docs;
          break;
        case ACTION_TYPES.COMPARE_PDF:
          label = '🔍 Compare';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_MAPPING:
          label = '🗺️ Mapping';
          variant = 'outline';
          disabled = !result?.mapped_fields;
          break;
        case ACTION_TYPES.VIEW_ERRORS:
          label = '⚠️ Errors';
          variant = 'outline';
          disabled = !result?.errors?.length;
          break;
        case ACTION_TYPES.EXPORT:
          label = '💾 Export';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.SAVE:
          label = '💾 Save';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        default:
          return;
      }

      // Extension-styled buttons with proper design system
      const getButtonClasses = () => {
        const baseClasses = 'inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1';

        if (disabled) {
          return `${baseClasses} bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200`;
        }

        switch (variant) {
          case 'primary':
            return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md`;
          case 'secondary':
            return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-sm hover:shadow-md`;
          case 'outline':
          default:
            return `${baseClasses} bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:ring-gray-500 shadow-sm hover:shadow-md`;
        }
      };

      buttons.push(
        <button
          key={action}
          onClick={() => handleAction(action)}
          disabled={disabled}
          className={getButtonClasses()}
          title={disabled ? 'Action not available' : `${label.replace(/[^\w\s]/g, '').trim()}`}
        >
          {label}
        </button>
      );
    });

    return buttons;
  };

  // Render compact mode for better UX - matching extension design
  if (compactMode) {
    const getContainerStyle = () => {
      if (!windowMode) { return {}; }

      let backgroundColor = 'white';
      let borderColor = '#e5e7eb';
      let boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';

      if (isActive) {
        backgroundColor = '#eff6ff';
        borderColor = '#3b82f6';
        boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 3px rgba(59, 130, 246, 0.1)';
      } else if (status === STEP_STATUS.ERROR) {
        backgroundColor = '#fef2f2';
        borderColor = '#ef4444';
        boxShadow = '0 1px 3px 0 rgba(239, 68, 68, 0.1), 0 1px 2px 0 rgba(239, 68, 68, 0.06)';
      } else if (status === STEP_STATUS.COMPLETED) {
        backgroundColor = '#f0fdf4';
        borderColor = '#22c55e';
        boxShadow = '0 1px 3px 0 rgba(34, 197, 94, 0.1), 0 1px 2px 0 rgba(34, 197, 94, 0.06)';
      } else if (status === STEP_STATUS.RUNNING) {
        backgroundColor = '#eff6ff';
        borderColor = '#3b82f6';
        boxShadow = '0 1px 3px 0 rgba(59, 130, 246, 0.1), 0 1px 2px 0 rgba(59, 130, 246, 0.06)';
      }

      return {
        position: 'relative',
        padding: '1.25rem',
        borderRadius: '0.75rem',
        border: `1px solid ${borderColor}`,
        backgroundColor,
        transition: 'all 0.2s ease-in-out',
        boxShadow,
        fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      };
    };

    const getStatusBarStyle = () => {
      if (!windowMode) { return {}; }

      let backgroundColor = '#d1d5db';
      if (status === STEP_STATUS.COMPLETED) { backgroundColor = '#22c55e'; } else if (status === STEP_STATUS.RUNNING) { backgroundColor = '#3b82f6'; } else if (status === STEP_STATUS.ERROR) { backgroundColor = '#ef4444'; }

      return {
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: '4px',
        borderRadius: '0.75rem 0 0 0.75rem',
        backgroundColor
      };
    };

    return (
      <div style={windowMode ? getContainerStyle() : {}} className={!windowMode ? `
        relative p-5 rounded-xl border transition-all duration-200 hover:shadow-md bg-white group
        ${isActive ? 'ring-2 ring-blue-500/20 shadow-lg border-blue-400 bg-blue-50/40' : 'border-gray-200 hover:border-gray-300 shadow-sm'}
        ${status === STEP_STATUS.ERROR ? 'border-red-400 bg-red-50/40 hover:bg-red-50/60' : ''}
        ${status === STEP_STATUS.COMPLETED ? 'border-green-400 bg-green-50/40 hover:bg-green-50/60' : ''}
        ${status === STEP_STATUS.RUNNING ? 'border-blue-400 bg-blue-50/40 hover:bg-blue-50/60' : ''}
      ` : ''}>
        {/* Status indicator bar */}
        <div style={windowMode ? getStatusBarStyle() : {}} className={!windowMode ? `absolute left-0 top-0 bottom-0 w-1 rounded-l-xl ${
          status === STEP_STATUS.COMPLETED ? 'bg-green-500' :
            status === STEP_STATUS.RUNNING ? 'bg-blue-500' :
              status === STEP_STATUS.ERROR ? 'bg-red-500' :
                'bg-gray-300'
        }` : ''} />
        {/* Compact Header */}
        <div style={windowMode ? {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        } : {}} className={!windowMode ? 'flex items-center justify-between' : ''}>
          <div style={windowMode ? {
            display: 'flex',
            alignItems: 'center',
            gap: '1rem'
          } : {}} className={!windowMode ? 'flex items-center space-x-4' : ''}>
            <div style={windowMode ? {
              padding: '0.5rem',
              borderRadius: '0.5rem',
              backgroundColor:
                status === STEP_STATUS.COMPLETED ? '#dcfce7' :
                  status === STEP_STATUS.RUNNING ? '#dbeafe' :
                    status === STEP_STATUS.ERROR ? '#fee2e2' :
                      '#f3f4f6',
              color:
                status === STEP_STATUS.COMPLETED ? '#16a34a' :
                  status === STEP_STATUS.RUNNING ? '#2563eb' :
                    status === STEP_STATUS.ERROR ? '#dc2626' :
                      '#9ca3af'
            } : {}} className={!windowMode ? `p-2 rounded-lg ${
              status === STEP_STATUS.COMPLETED ? 'bg-green-100 text-green-600' :
                status === STEP_STATUS.RUNNING ? 'bg-blue-100 text-blue-600' :
                  status === STEP_STATUS.ERROR ? 'bg-red-100 text-red-600' :
                    'bg-gray-100 text-gray-400'
            }` : ''}>
              <span style={windowMode ? { fontSize: '1.125rem' } : {}} className={!windowMode ? 'text-lg' : ''}>{step.icon}</span>
            </div>
            <div style={windowMode ? {
              flex: 1,
              minWidth: 0
            } : {}} className={!windowMode ? 'flex-1 min-w-0' : ''}>
              <h3 style={windowMode ? {
                fontWeight: '600',
                fontSize: '1rem',
                color: '#111827',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              } : {}} className={!windowMode ? 'font-semibold text-base text-gray-900 truncate' : ''}>{step.name}</h3>
              <p style={windowMode ? {
                fontSize: '0.875rem',
                color: '#4b5563',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              } : {}} className={!windowMode ? 'text-sm text-gray-600 truncate' : ''}>{step.description}</p>
              {status === STEP_STATUS.RUNNING && (
                <div style={windowMode ? {
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginTop: '0.5rem'
                } : {}} className={!windowMode ? 'flex items-center space-x-2 mt-2' : ''}>
                  <div style={windowMode ? {
                    width: '80px',
                    backgroundColor: '#e5e7eb',
                    borderRadius: '9999px',
                    height: '8px',
                    overflow: 'hidden'
                  } : {}} className={!windowMode ? 'w-20 bg-gray-200 rounded-full h-2 overflow-hidden' : ''}>
                    <div
                      style={windowMode ? {
                        background: 'linear-gradient(to right, #3b82f6, #2563eb)',
                        height: '8px',
                        borderRadius: '9999px',
                        transition: 'all 0.5s ease',
                        width: `${typeof progress === 'number' ? progress : 0}%`,
                        position: 'relative'
                      } : { width: `${typeof progress === 'number' ? progress : 0}%` }}
                      className={!windowMode ? 'bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 relative' : ''}
                    >
                      {windowMode && (
                        <div style={{
                          position: 'absolute',
                          inset: 0,
                          background: 'linear-gradient(to right, transparent, rgba(255,255,255,0.3), transparent)',
                          animation: 'pulse 2s infinite'
                        }} />
                      )}
                      {!windowMode && (
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
                      )}
                    </div>
                  </div>
                  <span style={windowMode ? {
                    fontSize: '0.75rem',
                    color: '#4b5563',
                    fontWeight: '500',
                    backgroundColor: '#f3f4f6',
                    padding: '0.125rem 0.5rem',
                    borderRadius: '0.25rem'
                  } : {}} className={!windowMode ? 'text-xs text-gray-600 font-medium bg-gray-100 px-2 py-0.5 rounded' : ''}>
                    {typeof progress === 'number' ? progress : 0}%
                  </span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className={`p-1.5 rounded-full ${
              status === STEP_STATUS.COMPLETED ? 'bg-green-100' :
                status === STEP_STATUS.RUNNING ? 'bg-blue-100' :
                  status === STEP_STATUS.ERROR ? 'bg-red-100' :
                    'bg-gray-100'
            }`}>
              <span className="text-xl">{getStatusIcon()}</span>
            </div>
            {(result || error) && (
              <button
                onClick={() => setExpanded(!expanded)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 group-hover:opacity-100 opacity-60"
                title="View details"
              >
                <span className="text-sm">{expanded ? '▼' : '▶'}</span>
              </button>
            )}
          </div>
        </div>

        {/* Compact Status - Extension styled */}
        <div className="flex items-center justify-between mt-2 text-xs">
          <span className={`font-medium ${
            status === STEP_STATUS.COMPLETED ? 'text-success-600' :
              status === STEP_STATUS.ERROR ? 'text-error-600' :
                status === STEP_STATUS.RUNNING ? 'text-primary-600' :
                  'text-gray-500'
          }`}>
            {timing > 0 ? `${formatTiming(timing)}` :
              status === STEP_STATUS.RUNNING ? 'Processing...' :
                status === STEP_STATUS.COMPLETED ? 'Completed' :
                  status === STEP_STATUS.ERROR ? 'Failed' : 'Pending'}
          </span>
          {result && result.confidence && (
            <span className="text-success-600 font-medium bg-success-100 px-2 py-0.5 rounded-full">
              {result.confidence}%
            </span>
          )}
        </div>

        {/* Error Display - Compact with extension styling */}
        {error && (
          <div className="mt-2 p-2 bg-error-50 border border-error-200 rounded-md text-xs text-error-700">
            <strong className="text-error-800">Error:</strong> {typeof error === 'string' ? error.substring(0, 100) + (error.length > 100 ? '...' : '') : 'Processing failed'}
          </div>
        )}

        {/* Expanded Details - Only when requested */}
        {expanded && (result || error) && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="space-y-2">
              {/* Quick Actions */}
              <div className="flex flex-wrap gap-1">
                {getActionButtons()}
              </div>

              {/* Summary Info */}
              {result && result.summary && (
                <div className="text-xs">
                  <strong className="text-gray-700">Summary:</strong>
                  <p className="text-gray-600 mt-1">{result.summary}</p>
                </div>
              )}

              {/* Detailed Error */}
              {error && (
                <div className="text-xs">
                  <strong className="text-red-700">Full Error:</strong>
                  <pre className="text-red-600 mt-1 whitespace-pre-wrap bg-red-50 p-2 rounded max-h-32 overflow-auto">
                    {typeof error === 'string' ? error : JSON.stringify(error, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Raw Data Sections - Only when specifically requested */}
        {showRawInput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
              <button
                onClick={() => setShowRawInput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
              </pre>
            </div>
          </div>
        )}

        {showRawOutput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
              <button
                onClick={() => setShowRawOutput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full detailed mode (existing implementation)
  return (
    <div className={`
      relative p-4 rounded-lg border-2 transition-all duration-200
      ${colors.bg} ${colors.border}
      ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
      ${status === STEP_STATUS.ERROR ? 'border-red-300 bg-red-50' : ''}
    `}>
      {/* Step Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className={`text-lg ${colors.icon}`}>{step.icon}</span>
          <div>
            <h3 className={`font-medium ${colors.text}`}>{step.name}</h3>
            <p className="text-xs text-gray-600">{step.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${getStatusColor()}`}>{getStatusIcon()}</span>
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {expanded ? '▼' : '▶'}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {status === STEP_STATUS.RUNNING && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${colors.progress}`}
              style={{ width: `${typeof progress === 'number' ? progress : 0}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">{typeof progress === 'number' ? progress : 0}%</div>
        </div>
      )}

      {/* Timing and Status */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
        <span>
          {timing > 0 ? `Completed in ${formatTiming(timing)}` :
            status === STEP_STATUS.RUNNING ? 'Processing...' :
              `Est. ${formatTiming(step.estimatedTime)}`}
        </span>
        {result && (
          <span className="text-green-600">
            {result.confidence ? `${result.confidence}% confidence` : 'Completed'}
          </span>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
          <strong>Error:</strong> {typeof error === 'string' ? error : JSON.stringify(error)}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-1 mb-2">
        {getActionButtons()}
      </div>

      {/* Expanded Details */}
      {expanded && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="text-xs space-y-2">
            <div>
              <strong className="text-gray-700">Outputs:</strong>
              <div className="mt-1 space-y-1">
                {step.outputs.map(output => (
                  <div key={output} className="flex justify-between">
                    <span className="text-gray-600">{output}:</span>
                    <span className="text-gray-800">
                      {result[output] ? '✓' : '✗'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {result.summary && (
              <div>
                <strong className="text-gray-700">Summary:</strong>
                <p className="text-gray-600 mt-1">{result.summary}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Raw Input Section */}
      {showRawInput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
            <button
              onClick={() => setShowRawInput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Raw Output Section */}
      {showRawOutput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
            <button
              onClick={() => setShowRawOutput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PipelineStepCard;
